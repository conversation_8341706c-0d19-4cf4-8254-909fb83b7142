<template>
  <div class="price-guess-game-layout">
    <!-- Header -->
    <div class="game-header">
      <div class="max-ctr">
        <div class="row items-center justify-between q-pa-lg">
          <div class="header-left">
            <div>
              <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">
                <router-link :to="{
                  name: 'rPriceGameStart',
                  params: {},
                }">
                  {{ gameTitle }}
                </router-link>
              </h1>
              <p class="text-body1 text-grey-7">
                {{ gameDesc }}
              </p>
            </div>
            <!-- <img
              class="hpg-logo-img q-mr-md"
              :src="logoUrl"
              alt="HousePriceGuess Logo"
            /> -->
          </div>
          <div class="header-right">
            <div class="row items-center q-gutter-sm">
              <!-- Admin Link -->
              <!-- <q-btn flat
                     dense
                     color="grey-7"
                     icon="settings"
                     size="sm"
                     @click="$router.push({ name: 'rPriceGuessPropertiesAdmin' })"
                     title="Property Admin" /> -->

              <div v-if="!!!isPriceGuessOnly">
                <!-- <q-btn flat
                       color="primary"
                       icon="arrow_back"
                       @click="$router.push({ name: 'rSubdomainRedir' })"
                       label="Back to Dossier" /> -->
              </div>
            </div>
          </div>
          <!-- 
          <div class="header-right"
               v-if="gameSessionId">
            <div class="game-progress">
              <div class="text-subtitle2 text-grey-7">Session: {{ gameSessionId.substring(0, 8) }}...</div>
              <div v-if="currentPropertyIndex !== null && totalProperties"
                   class="progress-info">
                <div class="text-body2 text-primary">
                  Property {{ currentPropertyIndex + 1 }} of {{ totalProperties }}
                </div>
                <q-linear-progress :value="(currentPropertyIndex + 1) / totalProperties"
                                   color="primary"
                                   size="6px"
                                   rounded
                                   class="q-mt-xs"
                                   style="width: 200px;" />
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-if="gameTitle"
         class="game-content">
      <!-- vif above solely for setting default currency - would love a better solution -->
      <router-view :game-session-id="gameSessionId"
                   :current-property-index="currentPropertyIndex"
                   :gameTitle="gameTitle"
                   :gameDefaultCurrency="gameDefaultCurrency"
                   :gameCommunitiesDetails="gameCommunitiesDetails"
                   :realtyGameSummary="realtyGameSummary"
                   :firstPropListing="firstPropListing"
                   :totalProperties="totalProperties"
                   :shareableResultsUrl="shareableResultsUrl"
                   :isCurrentUserSession="isCurrentUserSession"
                   @update-progress="handleProgressUpdate"
                   @game-complete="handleGameComplete" />
    </div>

    <!-- Footer -->
    <div class="game-footer">
      <div class="max-ctr">
        <div class="row items-center justify-between q-pa-md">
          <div class="footer-left">
            <!-- <q-btn flat
                   color="grey-7"
                   icon="home"
                   label="Home" /> -->
          </div>
          <div>
            <SocialSharing socialSharingPrompt=""
                           socialSharingTitle=""
                           urlProp=""></SocialSharing>
          </div>
          <!-- <div>
            <QrCodeShare urlProp=""
                           qrCodeTitle=""></QrCodeShare>
          </div> -->
          <div class="footer-right">
            <!-- <q-btn v-if="canShare"
                   flat
                   color="primary"
                   icon="share"
                   label="Share"
                   @click="shareGame" /> -->
          </div>
        </div>
      </div>
    </div>
    <!-- <CookieConsent /> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from '../composables/useRealtyGame'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
// import { useRealtyGameStore } from 'src/stores/realtyGame'
import useJsonLd from 'src/compose/useJsonLd.js'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'
// import logoUrl from '/icons/favicon-128x128.png'
//  'src/assets/hpg_logo_july_2025.png'
// // import CookieConsent from 'src/components/common/CookieConsent.vue'
// import QrCodeShare from "src/concerns/dossiers/components/sharing/QrCodeShare.vue"

// Define props
const props = defineProps({
  isPriceGuessOnly: {
    type: Boolean,
    default: false,
  },
})

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDesc,
  gameDefaultCurrency,
} = useRealtyGame()

// Initialize storage composable for shared computed values
const { getCurrentSessionId } = useRealtyGameStorage()

// Initialize JSON-LD functionality
const {
  initializeDefaultJsonLd,
  generateGameSessionSchema,
  addJsonLd,
  jsonLdMeta,
} = useJsonLd()

// Props from route
// const dossierUuid = computed(() => $route.params.dossierUuid)
const gameSessionId = computed(
  () => $route.query.session || $route.params.gameSessionId || ''
)

// Shared computed values that will be passed to child pages
const shareableResultsUrl = computed(() => {
  if (!gameSessionId.value || !$route.params.gameSlug) {
    return ''
  }
  let shareRoute = {
    name: 'rPriceGameResultsShareable',
    params: {
      gameSessionId: gameSessionId.value,
      gameSlug: $route.params.gameSlug
    }
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  return fullPath
})

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === gameSessionId.value
})

// Game state
const currentPropertyIndex = ref(null)
// const totalProperties = ref(null)

// UI state
// const showFooter = computed(() => {
//   return $route.name !== 'rPriceGameStart'
// })

// const canShare = computed(() => {
//   return $route.name === 'rPriceGuessResults' && gameSessionId.value
// })

// Event handlers
const handleProgressUpdate = (data) => {
  currentPropertyIndex.value = data.currentIndex
  // totalProperties.value = data.total
}

const handleGameComplete = (sessionId) => {
  // Navigate to results page
  $router.push({
    name: 'rPriceGuessResults',
    params: {
      // dossierUuid: dossierUuid.value,
      gameSessionId: sessionId,
    },
  })
}

const shareGame = () => {
  const url = window.location.href
  const text = `Check out my Property Price Challenge results!`

  if (navigator.share) {
    navigator
      .share({
        title: 'Property Price Challenge Results',
        text: text,
        url: url,
      })
      .catch((err) => {
        console.log('Error sharing:', err)
        fallbackShare(url, text)
      })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      $q.notify({
        message: 'Results URL copied to clipboard!',
        icon: 'content_copy',
        color: 'positive',
      })
    })
  } else {
    $q.notify({
      message: 'Share this URL: ' + url,
      icon: 'share',
      color: 'info',
      timeout: 5000,
    })
  }
}

const initializeGame = async () => {
  try {
    // await fetchPriceGuessData()
    await fetchPriceGuessData($route.params.gameSlug)
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error',
    })
  }
}

// Initialize JSON-LD
initializeDefaultJsonLd()

// Add game-specific JSON-LD
const gameData = {
  title: gameTitle.value || 'Property Price Challenge',
  description:
    gameDesc.value ||
    'Test your property knowledge with our interactive price guessing game.',
  totalPlayers: 0, // This could be fetched from analytics
}
addJsonLd(generateGameSessionSchema(gameData), 'game-schema')

// Use the meta data with useMeta
// useMeta needs to be called AFTER JsonLd has been correctly created!!
// Only set meta tags if we're not on a property route (which handles its own meta tags)
const isPropertyRoute = computed(() => $route.name === 'rPriceGameProperty')

// const layoutMetaData = computed(() => {
//   // For property routes, don't set meta tags in layout - they're handled by preFetch
//   if (isPropertyRoute.value) {
//     // Only include JSON-LD scripts for property routes
//     return {
//       script: jsonLdMeta.value
//     }
//   }
//   // Otherwise return full meta data for non-property routes
//   return metaData.value
// })

// Initialize progress from route if available
onMounted(() => {
  if ($route.params.propertyIndex) {
    currentPropertyIndex.value = parseInt($route.params.propertyIndex)
  }

  initializeGame()
})
// Watch route changes to update progress
watch(
  () => $route.params.propertyIndex,
  (newIndex) => {
    if (newIndex !== undefined) {
      currentPropertyIndex.value = parseInt(newIndex)
    }
  }
)
</script>

<style scoped>
.price-guess-game-layout {
  min-height: 100vh;
  /* background-color: #fafafa; */
  display: flex;
  flex-direction: column;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.game-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* position: sticky; */
  top: 0;
  z-index: 100;
}

.header-right {
  text-align: right;
}

.progress-info {
  margin-top: 0.5rem;
}

.game-content {
  flex: 1;
  overflow-y: auto;
}

.game-footer {
  background: white;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .game-header .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-right {
    text-align: left;
    width: 100%;
  }

  .progress-info .q-linear-progress {
    width: 100% !important;
  }

  .hpg-logo-img {
    height: 40px;
    margin-bottom: 0.5rem;
  }

  .header-left {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.hpg-logo-img {
  height: 60px;
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
  transition: transform 0.2s;
}

.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
}
</style>
